import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import LoginScreen from '@/app/(auth)/login';
import * as emailOtpService from '@/backend/supabase/services/auth/emailOtpService';
import { MobileAuthService } from '@/backend/supabase/services/auth/mobileAuthService';
import { AuthError } from '@supabase/supabase-js';

// Mock dependencies
jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({
    checkUserRole: jest.fn().mockResolvedValue({
      needsRoleSelection: false,
      needsOnboarding: false,
      role: 'customer',
    }),
  }),
}));

jest.mock('expo-router', () => ({
  router: {
    replace: jest.fn(),
  },
}));

jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
  }),
}));

// Spy on console methods to check for sensitive data logging
const consoleLogSpy = jest.spyOn(console, 'log');
const consoleErrorSpy = jest.spyOn(console, 'error');

describe('Login Screen - Security Tests', () => {
  afterEach(() => {
    jest.clearAllMocks();
    consoleLogSpy.mockClear();
    consoleErrorSpy.mockClear();
  });

  // Test 1: Input sanitization for email field to prevent XSS
  test('should sanitize email input to prevent XSS attacks', async () => {
    const sendEmailOTPSpy = jest.spyOn(emailOtpService, 'sendEmailOTP');
    const { getByLabelText, getByText } = render(<LoginScreen />);

    const emailInput = getByLabelText('Email Address');
    const submitButton = getByText('Continue with Email');

    const maliciousEmail = '<script>alert("XSS")</script>';
    fireEvent.changeText(emailInput, maliciousEmail);
    fireEvent.press(submitButton);

    await act(async () => {});

    // Expect validation to fail and not call the service
    expect(sendEmailOTPSpy).not.toHaveBeenCalled();
  });

  // Test 2: Input sanitization for mobile number to prevent non-numeric characters
  test('should sanitize mobile number input to only allow numeric characters', async () => {
    const signInSpy = jest.spyOn(MobileAuthService, 'signInWithMobilePassword');
    const { getByLabelText, getByText, getByPlaceholderText } = render(<LoginScreen />);

    // Switch to mobile/password auth
    fireEvent.press(getByText('Phone'));

    const mobileInput = getByPlaceholderText('9876543210');
    const passwordInput = getByLabelText('Password');
    const submitButton = getByText('Sign In');

    const maliciousMobile = '12345<script>alert("XSS")</script>67890';
    fireEvent.changeText(mobileInput, maliciousMobile);
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(submitButton);

    await act(async () => {});

    // The component should strip non-numeric characters.
    // Let's check if the service is called with the sanitized number.
    // The input sanitizes to '1234567890'
    expect(signInSpy).toHaveBeenCalledWith('1234567890', 'password123');
  });

  // Test 3: Ensure password is not logged on failed login
  test('should not log password on failed mobile/password login', async () => {
    const mockError = new AuthError('Invalid credentials');
    mockError.status = 400;

    jest.spyOn(MobileAuthService, 'signInWithMobilePassword').mockResolvedValue({
      error: mockError,
      data: { user: null, session: null },
    });

    const { getByLabelText, getByText, getByPlaceholderText } = render(<LoginScreen />);
    fireEvent.press(getByText('Phone'));

    const mobileInput = getByPlaceholderText('9876543210');
    const passwordInput = getByLabelText('Password');
    const submitButton = getByText('Sign In');

    const testPassword = 'sensitivePassword123';
    fireEvent.changeText(mobileInput, '1234567890');
    fireEvent.changeText(passwordInput, testPassword);
    fireEvent.press(submitButton);

    await act(async () => {});

    expect(consoleLogSpy).not.toHaveBeenCalledWith(expect.stringContaining(testPassword));
    expect(consoleErrorSpy).not.toHaveBeenCalledWith(expect.stringContaining(testPassword));
  });

  // Test 4: Attempt to bypass OTP verification
  test('should not allow navigation to dashboard without successful OTP verification', async () => {
    const { getByLabelText, getByText } = render(<LoginScreen />);
    const router = require('expo-router').router;

    // Simulate entering email and reaching OTP step
    jest.spyOn(emailOtpService, 'sendEmailOTP').mockResolvedValue({ success: true, message: 'OTP sent' });
    const emailInput = getByLabelText('Email Address');
    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.press(getByText('Continue with Email'));
    await act(async () => {});

    // Manually try to navigate without OTP
    // In a real app, this would be a deep link or other programmatic navigation
    // Here, we just check that the router.replace was not called with a dashboard route yet
    expect(router.replace).not.toHaveBeenCalledWith(expect.stringContaining('dashboard'));

    // Now, simulate failed OTP verification
    jest.spyOn(emailOtpService, 'verifyEmailOTP').mockResolvedValue({ success: false, message: 'Invalid OTP' });
    const otpInput = getByLabelText('Enter verification code').props.children[0]; // Access the actual OTPInput
    fireEvent(otpInput, 'onComplete', '000000');
    await act(async () => {});

    expect(router.replace).not.toHaveBeenCalledWith(expect.stringContaining('dashboard'));
  });

  // Test 5: Ensure error messages do not expose sensitive backend information
  test('should display generic error messages on failure', async () => {
    const detailedError = new AuthError('Postgres connection failed: timeout expired');
    detailedError.status = 500;
    
    jest.spyOn(MobileAuthService, 'signInWithMobilePassword').mockResolvedValue({
      error: detailedError,
      data: { user: null, session: null },
    });

    const { getByLabelText, getByText, findByText, getByPlaceholderText } = render(<LoginScreen />);
    fireEvent.press(getByText('Phone'));

    const mobileInput = getByPlaceholderText('9876543210');
    const passwordInput = getByLabelText('Password');
    const submitButton = getByText('Sign In');

    fireEvent.changeText(mobileInput, '1234567890');
    fireEvent.changeText(passwordInput, 'anypassword');
    fireEvent.press(submitButton);

    await act(async () => {});

    // Check for a generic error message, not the detailed one
    const errorElement = await findByText('Mobile login failed');
    expect(errorElement).toBeTruthy();
    expect(consoleErrorSpy).not.toHaveBeenCalledWith(expect.stringContaining('Postgres connection failed'));
  });

  // Test 6: Prevent auth requests when offline
  test('should disable login buttons and not send requests when offline', async () => {
    // Mock network status to be offline
    jest.mock('@/src/components/ui/NetworkStatusBanner', () => ({
      useNetworkStatus: () => ({ isOnline: false }),
    }));

    const sendEmailOTPSpy = jest.spyOn(emailOtpService, 'sendEmailOTP');
    const { getByText } = render(<LoginScreen />);

    const emailSubmitButton = getByText('Continue with Email');
    const googleSubmitButton = getByText('Continue with Google');

    expect(emailSubmitButton.props.accessibilityState.disabled).toBe(true);
    expect(googleSubmitButton.props.accessibilityState.disabled).toBe(true);

    fireEvent.press(emailSubmitButton);
    await act(async () => {});

    expect(sendEmailOTPSpy).not.toHaveBeenCalled();
  });
});