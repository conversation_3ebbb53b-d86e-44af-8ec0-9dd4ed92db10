import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { LoginForm } from '@/app/(main)/login/LoginForm';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { sendOTP, verifyOTP, loginWithMobilePassword } from '@/app/(main)/login/actions';
import { createClient } from '@/utils/supabase/client';
import { getPostLoginRedirectPath } from '@/lib/actions/redirectAfterLogin';

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock server actions
jest.mock('@/app/(main)/login/actions', () => ({
  sendOTP: jest.fn(),
  verifyOTP: jest.fn(),
  loginWithMobilePassword: jest.fn(),
}));

// Type the mocked functions
const mockSendOTP = sendOTP as jest.MockedFunction<typeof sendOTP>;
const mockVerifyOTP = verifyOTP as jest.MockedFunction<typeof verifyOTP>;
const mockLoginWithMobilePassword = loginWithMobilePassword as jest.MockedFunction<typeof loginWithMobilePassword>;

// Mock Supabase client and redirect action
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn(() => Promise.resolve({ data: { user: { id: 'user-123' } }, error: null })),
    },
  })),
}));

jest.mock('@/lib/actions/redirectAfterLogin', () => ({
  getPostLoginRedirectPath: jest.fn(() => Promise.resolve('/dashboard')),
}));

// Mock child components to simplify unit testing LoginForm's logic
jest.mock('@/app/(main)/login/components/EmailOTPForm', () => ({
  EmailOTPForm: jest.fn(({ onEmailSubmit, onOTPSubmit, onResendOTP, onBackToEmail, step, email, countdown, isPending }) => (
    <div data-testid="email-otp-form">
      {step === 'email' ? (
        <button onClick={() => onEmailSubmit({ email: '<EMAIL>' })}>Submit Email</button>
      ) : (
        <div>
          <span>OTP for {email}</span>
          <button onClick={() => onOTPSubmit({ otp: '123456' })}>Submit OTP</button>
          <button
            onClick={onResendOTP}
            disabled={countdown > 0}
            data-testid="resend-otp-button"
          >
            {countdown > 0 ? `Resend OTP in ${countdown}s` : 'Resend OTP'}
          </button>
          <button onClick={onBackToEmail}>Back to Email</button>
        </div>
      )}
      {isPending && <span>Loading...</span>}
    </div>
  )),
}));

jest.mock('@/app/(main)/login/components/MobilePasswordForm', () => ({
  MobilePasswordForm: jest.fn(({ onSubmit, isPending }) => (
    <div data-testid="mobile-password-form">
      <button onClick={() => onSubmit({ mobile: '**********', password: 'password123' })}>Submit Mobile/Password</button>
      {isPending && <span>Loading...</span>}
    </div>
  )),
}));

jest.mock('@/app/(main)/login/components/AuthMethodToggle', () => ({
  AuthMethodToggle: jest.fn(({ authMethod, step, onMethodChange }) => (
    <div data-testid="auth-method-toggle">
      <button onClick={() => onMethodChange('email-otp')}>Email OTP</button>
      <button onClick={() => onMethodChange('mobile-password')}>Mobile/Password</button>
      <span>Current method: {authMethod}, Step: {step}</span>
    </div>
  )),
}));

jest.mock('@/app/(main)/login/components/SocialLoginButton', () => ({
  SocialLoginButton: jest.fn(({ redirectSlug, message, disabled }) => (
    <button data-testid="social-login-button" disabled={disabled}>
      Social Login (Redirect: {redirectSlug}, Message: {message})
    </button>
  )),
}));

describe('LoginForm', () => {
  const mockPush = jest.fn();
  const mockUseRouter = useRouter as jest.Mock;
  const mockUseSearchParams = useSearchParams as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({ push: mockPush });
    mockUseSearchParams.mockReturnValue(new URLSearchParams());
  });

  it('renders correctly with default email-otp method and email step', () => {
    render(<LoginForm />);
    expect(screen.getByText('Welcome to Dukancard')).toBeInTheDocument();
    expect(screen.getByText('Sign in or create your account with email')).toBeInTheDocument();
    expect(screen.getByTestId('email-otp-form')).toBeInTheDocument();
    expect(screen.queryByTestId('mobile-password-form')).not.toBeInTheDocument();
  });

  it('switches to mobile-password form when toggle is clicked', () => {
    render(<LoginForm />);
    fireEvent.click(screen.getByText('Mobile/Password'));
    expect(screen.getByText('Sign in with your mobile number and password')).toBeInTheDocument();
    expect(screen.getByTestId('mobile-password-form')).toBeInTheDocument();
    expect(screen.queryByTestId('email-otp-form')).not.toBeInTheDocument();
  });

  it('handles successful email OTP submission and transitions to OTP step', async () => {
    mockSendOTP.mockResolvedValueOnce({ success: true, message: 'OTP sent successfully' });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Submit Email'));

    await waitFor(() => {
      expect(mockSendOTP).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(toast.success).toHaveBeenCalledWith('OTP sent!', { description: 'OTP sent successfully' });
      expect(screen.getByText('Enter Verification Code')).toBeInTheDocument();
      expect(screen.getByText('Check your email for the 6-digit code')).toBeInTheDocument();
      expect(screen.getByText('<NAME_EMAIL>')).toBeInTheDocument();
    });
  });

  it('handles failed email OTP submission and shows error toast', async () => {
    mockSendOTP.mockResolvedValueOnce({ success: false, error: 'Invalid email' });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Submit Email'));

    await waitFor(() => {
      expect(mockSendOTP).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(toast.error).toHaveBeenCalledWith('Failed to send OTP', { description: 'Invalid email' });
      expect(screen.queryByText('Enter Verification Code')).not.toBeInTheDocument(); // Should not transition
    });
  });

  it('handles configuration error for email OTP submission', async () => {
    mockSendOTP.mockResolvedValueOnce({ success: false, error: 'Email rate limit exceeded', isConfigurationError: true });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Submit Email'));

    await waitFor(() => {
      expect(mockSendOTP).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(toast.error).toHaveBeenCalledWith('Configuration Error', {
        description: 'Email rate limit exceeded',
        duration: 10000,
      });
      expect(screen.queryByText('Enter Verification Code')).not.toBeInTheDocument(); // Should not transition
    });
  });

  it('handles successful OTP verification and redirects', async () => {
    mockSendOTP.mockResolvedValueOnce({ success: true, message: 'OTP sent successfully' });
    mockVerifyOTP.mockResolvedValueOnce({ success: true, data: {}, message: 'OTP verified successfully' });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Submit Email')); // Transition to OTP step
    await waitFor(() => expect(screen.getByText('<NAME_EMAIL>')).toBeInTheDocument());

    fireEvent.click(screen.getByText('Submit OTP'));

    await waitFor(() => {
      expect(mockVerifyOTP).toHaveBeenCalledWith({ email: '<EMAIL>', token: '123456' });
      expect(toast.success).toHaveBeenCalledWith('Sign in successful!', { description: 'Redirecting to your dashboard...' });
      expect(createClient).toHaveBeenCalled();
      expect(getPostLoginRedirectPath).toHaveBeenCalledWith(expect.any(Object), 'user-123');
      expect(mockPush).toHaveBeenCalledWith('/dashboard');
    });
  });

  it('handles failed OTP verification and shows error toast', async () => {
    mockSendOTP.mockResolvedValueOnce({ success: true, message: 'OTP sent successfully' });
    mockVerifyOTP.mockResolvedValueOnce({ success: false, error: 'Invalid OTP' });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Submit Email')); // Transition to OTP step
    await waitFor(() => expect(screen.getByText('<NAME_EMAIL>')).toBeInTheDocument());

    fireEvent.click(screen.getByText('Submit OTP'));

    await waitFor(() => {
      expect(mockVerifyOTP).toHaveBeenCalledWith({ email: '<EMAIL>', token: '123456' });
      expect(toast.error).toHaveBeenCalledWith('OTP verification failed', { description: 'Invalid OTP' });
      expect(mockPush).not.toHaveBeenCalled(); // Should not redirect
    });
  });

  it('handles resend OTP button state and functionality', async () => {
    mockSendOTP.mockResolvedValue({ success: true, message: 'OTP sent successfully' });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Submit Email'));
    await waitFor(() => expect(screen.getByText('<NAME_EMAIL>')).toBeInTheDocument());

    // Initially the button should be disabled with countdown
    expect(screen.getByTestId('resend-otp-button')).toBeDisabled();
    expect(screen.getByText(/Resend OTP in \d+s/)).toBeInTheDocument();

    // Test that the resend functionality works (we'll test the actual countdown in integration tests)
    // For unit testing, we focus on the core logic rather than timer behavior
    expect(mockSendOTP).toHaveBeenCalledTimes(1); // Initial send
  });

  it('handles back to email action', async () => {
    mockSendOTP.mockResolvedValueOnce({ success: true, message: 'OTP sent successfully' });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Submit Email'));
    await waitFor(() => expect(screen.getByText('<NAME_EMAIL>')).toBeInTheDocument());

    await act(async () => {
      fireEvent.click(screen.getByText('Back to Email'));
    });

    await waitFor(() => {
      expect(screen.getByText('Welcome to Dukancard')).toBeInTheDocument();
      expect(screen.getByText('Sign in or create your account with email')).toBeInTheDocument();
      expect(screen.getByTestId('email-otp-form')).toBeInTheDocument();
      expect(screen.queryByText('<NAME_EMAIL>')).not.toBeInTheDocument();
    });
  });

  it('handles successful mobile/password login and redirects', async () => {
    mockLoginWithMobilePassword.mockResolvedValueOnce({ success: true, data: {}, message: 'Login successful' });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Mobile/Password')); // Switch to mobile/password form
    await waitFor(() => expect(screen.getByTestId('mobile-password-form')).toBeInTheDocument());

    await act(async () => {
      fireEvent.click(screen.getByText('Submit Mobile/Password'));
    });

    await waitFor(() => {
      expect(mockLoginWithMobilePassword).toHaveBeenCalledWith({ mobile: '**********', password: 'password123' });
      expect(toast.success).toHaveBeenCalledWith('Sign in successful!', { description: 'Redirecting to your dashboard...' });
      expect(createClient).toHaveBeenCalled();
      expect(getPostLoginRedirectPath).toHaveBeenCalledWith(expect.any(Object), 'user-123');
      expect(mockPush).toHaveBeenCalledWith('/dashboard');
    });
  });

  it('handles failed mobile/password login and shows error toast', async () => {
    mockLoginWithMobilePassword.mockResolvedValueOnce({ success: false, error: 'Invalid credentials' });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Mobile/Password')); // Switch to mobile/password form
    await waitFor(() => expect(screen.getByTestId('mobile-password-form')).toBeInTheDocument());

    await act(async () => {
      fireEvent.click(screen.getByText('Submit Mobile/Password'));
    });

    await waitFor(() => {
      expect(mockLoginWithMobilePassword).toHaveBeenCalledWith({ mobile: '**********', password: 'password123' });
      expect(toast.error).toHaveBeenCalledWith('Login failed', { description: 'Invalid credentials' });
      expect(mockPush).not.toHaveBeenCalled(); // Should not redirect
    });
  });

  it('displays message from search params', () => {
    mockUseSearchParams.mockReturnValue(new URLSearchParams('?message=Test%20message'));
    render(<LoginForm />);
    expect(screen.getByText('Test message')).toBeInTheDocument();
    expect(screen.getByText('Test message').closest('div')).toHaveClass('bg-green-500/10 text-green-600 dark:text-green-400');
  });

  it('displays error message from search params', () => {
    mockUseSearchParams.mockReturnValue(new URLSearchParams('?message=Login%20failed%20error'));
    render(<LoginForm />);
    expect(screen.getByText('Login failed error')).toBeInTheDocument();
    expect(screen.getByText('Login failed error').closest('div')).toHaveClass('bg-destructive/10 text-destructive');
  });

  it('redirects to specified slug if present in search params', async () => {
    mockUseSearchParams.mockReturnValue(new URLSearchParams('?redirect=my-profile'));
    mockSendOTP.mockResolvedValueOnce({ success: true, message: 'OTP sent successfully' });
    mockVerifyOTP.mockResolvedValueOnce({ success: true, data: {}, message: 'OTP verified successfully' });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Submit Email'));
    await waitFor(() => expect(screen.getByText('<NAME_EMAIL>')).toBeInTheDocument());

    await act(async () => {
      fireEvent.click(screen.getByText('Submit OTP'));
    });

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/my-profile');
    });
  });

  it('redirects to specified slug with message if present in search params', async () => {
    mockUseSearchParams.mockReturnValue(new URLSearchParams('?redirect=my-profile&message=Welcome'));
    mockSendOTP.mockResolvedValueOnce({ success: true, message: 'OTP sent successfully' });
    mockVerifyOTP.mockResolvedValueOnce({ success: true, data: {}, message: 'OTP verified successfully' });

    render(<LoginForm />);
    fireEvent.click(screen.getByText('Submit Email'));
    await waitFor(() => expect(screen.getByText('<NAME_EMAIL>')).toBeInTheDocument());

    await act(async () => {
      fireEvent.click(screen.getByText('Submit OTP'));
    });

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/my-profile?message=Welcome');
    });
  });

  it('disables social login button when pending', async () => {
    mockSendOTP.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({ success: true, message: 'OTP sent' }), 100)));
    render(<LoginForm />);
    fireEvent.click(screen.getByText('Submit Email'));
    expect(screen.getByTestId('social-login-button')).toBeDisabled();
    await waitFor(() => expect(screen.getByTestId('social-login-button')).not.toBeDisabled());
  });
});