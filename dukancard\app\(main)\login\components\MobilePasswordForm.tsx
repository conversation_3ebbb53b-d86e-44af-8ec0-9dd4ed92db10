"use client";

import { MobilePasswordLoginSchema } from "@/lib/schemas/authSchemas";

interface MobilePasswordFormProps {
  isPending: boolean;
  onSubmit: (_values: z.infer<typeof MobilePasswordLoginSchema>) => void;
}

export function MobilePasswordForm({ isPending, onSubmit }: MobilePasswordFormProps) {
  const form = useForm<z.infer<typeof MobilePasswordLoginSchema>>({
    resolver: zodResolver(MobilePasswordLoginSchema),
    defaultValues: {
      mobile: "",
      password: "",
    },
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-4 sm:space-y-6"
      >
        <FormField
          control={form.control}
          name="mobile"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-foreground text-sm sm:text-base">
                Mobile Number
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                    +91
                  </div>
                  <Input
                    placeholder="9876543210"
                    type="tel"
                    {...field}
                    onChange={(e) => {
                      let value = e.target.value;
                      // Remove any +91 prefix if user enters it
                      value = value.replace(/^\+91/, '');
                      // Only allow numeric input
                      value = value.replace(/\D/g, '');
                      // Limit to 10 digits for mobile numbers
                      if (value.length > 10) {
                        value = value.slice(0, 10);
                      }
                      field.onChange(value);
                    }}
                    onKeyDown={(e) => {
                      const isNumeric = /^[0-9]$/.test(e.key);
                      const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);
                      if (!isNumeric && !isControl) {
                        e.preventDefault();
                      }
                    }}
                    className="pl-12 bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"
                    maxLength={10}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-foreground text-sm sm:text-base">
                Password
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="••••••••"
                  type="password"
                  {...field}
                  className="bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base"
          disabled={isPending}
        >
          {isPending ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              Signing in...
            </>
          ) : (
            <>
              Sign In <ArrowRight className="w-5 h-5 ml-2" />
            </>
          )}
        </Button>

        <div className="text-center mt-4 text-xs sm:text-sm">
          <span className="text-muted-foreground">
            Don&apos;t have an account?{" "}
            <Link
              href="/register"
              className="text-primary dark:text-[var(--brand-gold)] hover:underline font-medium"
            >
              Register here
            </Link>
          </span>
        </div>
      </form>
    </Form>
  );
}
