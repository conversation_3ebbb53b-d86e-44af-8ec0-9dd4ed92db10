import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import { Platform } from 'react-native';
import LoginScreen from '@/app/(auth)/login';

// Mock dependencies
jest.mock('@/hooks/useAuth', () => ({
  __esModule: true,
  default: () => ({
    signInWithGoogle: jest.fn().mockResolvedValue({ error: null }),
    sendMobileOTP: jest.fn().mockResolvedValue({ error: null }),
    verifyMobileOTP: jest.fn().mockResolvedValue({ data: { session: {} }, error: null }),
  }),
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    replace: jest.fn(),
  }),
}));

describe('Login Screen - Framework Specific Tests', () => {
  // Test 1: Platform-specific UI rendering
  test('applies platform-specific styles', () => {
    const androidStyle = { backgroundColor: 'blue' };
    const iosStyle = { backgroundColor: 'green' };

    Platform.OS = 'android';
    const { getByTestId, unmount } = render(<LoginScreen />);
    const container = getByTestId('login-container');
    // This is a placeholder. Actual style properties should be checked.
    // expect(container.props.style).toMatchObject(expect.objectContaining(androidStyle));
    unmount();

    Platform.OS = 'ios';
    const { getByTestId: getByTestIdIOS } = render(<LoginScreen />);
    const iosContainer = getByTestIdIOS('login-container');
    // This is a placeholder. Actual style properties should be checked.
    // expect(iosContainer.props.style).toMatchObject(expect.objectContaining(iosStyle));
  });

  // Test 2: Accessibility properties
  test('should have correct accessibility labels and testIDs', () => {
    const { getByTestId, getByLabelText } = render(<LoginScreen />);
    expect(getByTestId('mobile-input')).toBeTruthy();
    expect(getByLabelText('Enter Mobile Number')).toBeTruthy();
    expect(getByTestId('send-otp-button')).toBeTruthy();
    expect(getByLabelText('Send OTP')).toBeTruthy();
    expect(getByTestId('google-signin-button')).toBeTruthy();
    expect(getByLabelText('Sign in with Google')).toBeTruthy();
  });

  // Test 3: Navigation on successful login
  test('navigates to the home screen on successful login', async () => {
    const { getByTestId } = render(<LoginScreen />);
    const mobileInput = getByTestId('mobile-input');
    const sendOtpButton = getByTestId('send-otp-button');

    fireEvent.changeText(mobileInput, '1234567890');
    fireEvent.press(sendOtpButton);

    // NOTE: This is a simplified example. In a real scenario, you would
    // also need to handle the OTP input and verification process.
    // We are assuming the `verifyMobileOTP` mock is sufficient for this test.

    // const otpInput = getByTestId('otp-input');
    // fireEvent.changeText(otpInput, '123456');
    // const verifyOtpButton = getByTestId('verify-otp-button');
    // fireEvent.press(verifyOtpButton);

    // await act(async () => {
    //   // Wait for promises to resolve
    // });

    // expect(useRouter().replace).toHaveBeenCalledWith('/(tabs)/');
  });

  // Test 4: Responsive design
  test('adapts to different screen sizes', () => {
    const { getByTestId, rerender } = render(<LoginScreen />);
    const container = getByTestId('login-container');

    // Initial layout
    // expect(container.props.style.flexDirection).toBe('column');

    // Change screen dimensions and re-render
    // Dimensions.set({ window: { width: 800, height: 600 } });
    // rerender(<LoginScreen />);

    // Assert that the layout has changed for a wider screen
    // expect(container.props.style.flexDirection).toBe('row');
  });

  // Test 5: Google Sign-In
  test('calls signInWithGoogle on button press', async () => {
    const { getByTestId } = render(<LoginScreen />);
    const googleSignInButton = getByTestId('google-signin-button');

    await act(async () => {
      fireEvent.press(googleSignInButton);
    });

    // expect(useAuth().signInWithGoogle).toHaveBeenCalled();
  });

  // Test 6: Deep linking handling
  test('handles deep linking for login', async () => {
    // This requires more setup with mocking linking events
    // For now, this is a placeholder for the logic
  });
});