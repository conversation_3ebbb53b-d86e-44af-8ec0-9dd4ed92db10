"use client";

import { motion } from "framer-motion";
import { Heart, Users, Star, Calendar } from "lucide-react";
import { formatIndianNumberShort } from "@/lib/utils";

interface EnhancedMetricsCardsProps {
  totalLikes: number;
  totalSubscriptions: number;
  averageRating: number;
  createdAt?: Date | string;
  // Interactive button props
  isSubscribed: boolean;
  hasLiked: boolean;
  isLoadingInteraction: boolean;
  onSubscribe: () => void;
  onUnsubscribe: () => void;
  onLike: () => void;
  onUnlike: () => void;
  onReviewClick: () => void;
  isOwnBusiness: boolean;
}

const cardVariants = {
  hidden: { opacity: 0, y: 20, scale: 0.95 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      delay: i * 0.1,
      duration: 0.6,
      ease: "easeOut"
    }
  })
};

export default function EnhancedMetricsCards({
  totalLikes,
  totalSubscriptions,
  averageRating,
  createdAt,
  isSubscribed,
  hasLiked,
  isLoadingInteraction,
  onSubscribe,
  onUnsubscribe,
  onLike,
  onUnlike,
  onReviewClick,
  isOwnBusiness,
}: EnhancedMetricsCardsProps) {
  // Calculate platform membership duration
  const membershipDays = createdAt ?
    Math.floor((new Date().getTime() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24)) : 0;

  const getMembershipLabel = (days: number): string => {
    if (days === 0) return "New Member";
    if (days < 30) return `${days} days on platform`;
    if (days < 365) return `${Math.floor(days / 30)} months on platform`;
    return `${Math.floor(days / 365)} years on platform`;
  };

  const metrics = [
    {
      icon: Heart,
      value: formatIndianNumberShort(totalLikes),
      label: "Likes",
      color: "text-red-500",
      bgGradient: "bg-white dark:bg-black",
      glowColor: "shadow-red-500/40",
      borderColor: "border-red-200/50 dark:border-red-700/50",
      innerGlow: "bg-red-500/5 dark:bg-red-500/10",
      circleColor: "bg-red-500/30",
      circleGlow: "shadow-red-500/60",
      action: hasLiked ? onUnlike : onLike,
      actionLabel: hasLiked ? "Unlike" : "Like",
      isActive: hasLiked,
      disabled: isOwnBusiness
    },
    {
      icon: Users,
      value: formatIndianNumberShort(totalSubscriptions),
      label: "Followers",
      color: "text-blue-500",
      bgGradient: "bg-white dark:bg-black",
      glowColor: "shadow-blue-500/40",
      borderColor: "border-blue-200/50 dark:border-blue-700/50",
      innerGlow: "bg-blue-500/5 dark:bg-blue-500/10",
      circleColor: "bg-blue-500/30",
      circleGlow: "shadow-blue-500/60",
      action: isSubscribed ? onUnsubscribe : onSubscribe,
      actionLabel: isSubscribed ? "Unsubscribe" : "Subscribe",
      isActive: isSubscribed,
      disabled: isOwnBusiness
    },
    {
      icon: Star,
      value: averageRating.toFixed(1),
      label: "Rating",
      color: "text-yellow-500",
      bgGradient: "bg-white dark:bg-black",
      glowColor: "shadow-yellow-500/40",
      borderColor: "border-yellow-200/50 dark:border-yellow-700/50",
      innerGlow: "bg-yellow-500/5 dark:bg-yellow-500/10",
      circleColor: "bg-yellow-500/30",
      circleGlow: "shadow-yellow-500/60",
      action: onReviewClick,
      actionLabel: "Review",
      isActive: false,
      disabled: false
    },
    {
      icon: Calendar,
      value: getMembershipLabel(membershipDays),
      label: "Platform Member",
      color: "text-purple-500",
      bgGradient: "bg-white dark:bg-black",
      glowColor: "shadow-purple-500/40",
      borderColor: "border-purple-200/50 dark:border-purple-700/50",
      innerGlow: "bg-purple-500/5 dark:bg-purple-500/10",
      circleColor: "bg-purple-500/30",
      circleGlow: "shadow-purple-500/60",
      action: undefined,
      actionLabel: "",
      isActive: false,
      disabled: true
    }
  ];

  return (
    <div className="p-6 border-b border-neutral-200/30 dark:border-neutral-700/30">
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => {
          const IconComponent = metric.icon;
          return (
            <motion.div
              key={metric.label}
              custom={index}
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
              className={`
                relative overflow-hidden rounded-xl p-4
                ${metric.bgGradient}
                border ${metric.borderColor}
                ${metric.glowColor} shadow-lg
                hover:shadow-xl hover:${metric.glowColor}
                transition-all duration-300
                group cursor-default
              `}
            >
              {/* Strong inner glow effect that fills the card */}
              <div className={`absolute inset-0 ${metric.innerGlow} opacity-80 group-hover:opacity-100 transition-opacity duration-300`} />

              {/* Content */}
              <div className="relative z-10">
                {/* Icon */}
                <div className="flex items-center justify-center mb-3">
                  <div className={`p-2 rounded-lg bg-white/50 dark:bg-neutral-800/50 ${metric.glowColor} group-hover:shadow-md transition-all duration-300`}>
                    <IconComponent className={`w-5 h-5 ${metric.color}`} />
                  </div>
                </div>

                {/* Value */}
                <div className="text-center">
                  <div className="text-xl font-bold text-neutral-900 dark:text-neutral-100 mb-1 group-hover:scale-110 transition-transform duration-300">
                    {metric.value}
                  </div>
                  <div className="text-xs font-medium text-neutral-600 dark:text-neutral-400">
                    {metric.label}
                  </div>
                </div>

                {/* Interactive Button */}
                {metric.action && metric.actionLabel && (
                  <div className="mt-3 relative z-20">
                    <motion.button
                      onClick={metric.action}
                      disabled={metric.disabled || isLoadingInteraction}
                      whileHover={{ scale: metric.disabled ? 1 : 1.05 }}
                      whileTap={{ scale: metric.disabled ? 1 : 0.95 }}
                      className={`
                        w-full px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200
                        ${metric.disabled
                          ? 'bg-neutral-100 dark:bg-neutral-800 text-neutral-400 dark:text-neutral-600 cursor-not-allowed'
                          : metric.isActive
                            ? `${metric.color} bg-current/10 border border-current/20 hover:bg-current/20 cursor-pointer`
                            : `${metric.color} bg-current/5 border border-current/10 hover:bg-current/15 hover:border-current/30 cursor-pointer`
                        }
                        ${isLoadingInteraction ? 'opacity-50 cursor-wait' : ''}
                      `}
                    >
                      {isLoadingInteraction ? '...' : metric.actionLabel}
                    </motion.button>
                  </div>
                )}
              </div>

              {/* Strong decorative colored glow elements */}
              <div className={`absolute -top-2 -right-2 w-10 h-10 ${metric.circleColor} rounded-full ${metric.circleGlow} opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md`} />
              <div className={`absolute -bottom-2 -left-2 w-8 h-8 ${metric.circleColor} rounded-full ${metric.circleGlow} opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md`} />
            </motion.div>
          );
        })}
      </div>


    </div>
  );
}
