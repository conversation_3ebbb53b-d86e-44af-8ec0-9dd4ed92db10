import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import LoginScreen from '@/app/(auth)/login';
import { AuthProvider } from '@/src/contexts/AuthContext';
import * as emailOtpService from '@/backend/supabase/services/auth/emailOtpService';
import * as nativeGoogleAuth from '@/backend/supabase/services/auth/nativeGoogleAuth2025';
import { MobileAuthService } from '@/backend/supabase/services/auth/mobileAuthService';
import { router } from 'expo-router';

// Mock external dependencies
jest.mock('expo-router', () => ({
  router: {
    replace: jest.fn(),
  },
}));

jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({
    checkUserRole: jest.fn().mockResolvedValue({
      needsRoleSelection: false,
      needsOnboarding: false,
      role: 'customer',
    }),
  }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

jest.mock('@/backend/supabase/services/auth/emailOtpService', () => ({
  sendEmailOTP: jest.fn(),
  verifyEmailOTP: jest.fn(),
  validateEmail: jest.fn((email) => ({ isValid: !!email && email.includes('@') })),
  validateOTP: jest.fn((otp) => ({ isValid: otp && otp.length === 6 })),
}));

jest.mock('@/backend/supabase/services/auth/nativeGoogleAuth2025', () => ({
  signInWithGoogleNative: jest.fn(),
}));

jest.mock('@/backend/supabase/services/auth/mobileAuthService', () => ({
  MobileAuthService: {
    signInWithMobilePassword: jest.fn(),
  },
}));

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    isDark: false,
    colors: {
      primary: '#000',
      background: '#fff',
      text: '#000',
      textSecondary: '#666',
    },
  }),
}));

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}));

const renderLoginScreen = () => {
  return render(
    <AuthProvider>
      <LoginScreen />
    </AuthProvider>
  );
};

describe('Login Page Integration Tests', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('Email OTP Flow', () => {
    it('should allow a user to log in successfully with a valid email and OTP', async () => {
      (emailOtpService.sendEmailOTP as jest.Mock).mockResolvedValue({ success: true });
      (emailOtpService.verifyEmailOTP as jest.Mock).mockResolvedValue({ success: true });

      const { getByPlaceholderText, getByText, getByLabelText } = renderLoginScreen();

      const emailInput = getByPlaceholderText('Enter your email address');
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(getByText('Continue with Email'));

      await waitFor(() => {
        expect(emailOtpService.sendEmailOTP).toHaveBeenCalledWith('<EMAIL>');
      });

      const otpInput = getByLabelText('Enter verification code');
      fireEvent.changeText(otpInput, '123456');

      await waitFor(() => {
        expect(emailOtpService.verifyEmailOTP).toHaveBeenCalledWith('<EMAIL>', '123456');
        expect(router.replace).toHaveBeenCalledWith('/(dashboard)/customer');
      });
    });

    it('should show an error for an invalid email format', async () => {
        (emailOtpService.validateEmail as jest.Mock).mockReturnValue({ isValid: false, message: 'Invalid email format' });
        const { getByPlaceholderText, getByText, findByText } = renderLoginScreen();
  
        const emailInput = getByPlaceholderText('Enter your email address');
        fireEvent.changeText(emailInput, 'invalid-email');
        fireEvent.press(getByText('Continue with Email'));
  
        const errorMessage = await findByText('Invalid email format');
        expect(errorMessage).toBeTruthy();
        expect(emailOtpService.sendEmailOTP).not.toHaveBeenCalled();
    });

    it('should display an error message if sending OTP fails', async () => {
        (emailOtpService.sendEmailOTP as jest.Mock).mockResolvedValue({ success: false, message: 'Failed to send OTP' });
        const { getByPlaceholderText, getByText, findByText } = renderLoginScreen();

        fireEvent.changeText(getByPlaceholderText('Enter your email address'), '<EMAIL>');
        fireEvent.press(getByText('Continue with Email'));

        const errorMessage = await findByText('Failed to send OTP');
        expect(errorMessage).toBeTruthy();
    });

    it('should handle OTP verification failure from the server', async () => {
        (emailOtpService.sendEmailOTP as jest.Mock).mockResolvedValue({ success: true });
        (emailOtpService.verifyEmailOTP as jest.Mock).mockResolvedValue({ success: false, message: 'Invalid OTP code' });

        const { getByPlaceholderText, getByText, getByLabelText, findByText } = renderLoginScreen();

        fireEvent.changeText(getByPlaceholderText('Enter your email address'), '<EMAIL>');
        fireEvent.press(getByText('Continue with Email'));

        await waitFor(() => expect(getByLabelText('Enter verification code')).toBeTruthy());

        const otpInput = getByLabelText('Enter verification code');
        fireEvent.changeText(otpInput, '123456');

        await waitFor(async () => {
            const errorMessage = await findByText('Invalid OTP code');
            expect(errorMessage).toBeTruthy();
        });
        expect(router.replace).not.toHaveBeenCalled();
    });

    it('should allow resending OTP after the timer expires', async () => {
        jest.useFakeTimers();
        (emailOtpService.sendEmailOTP as jest.Mock).mockResolvedValue({ success: true });
        const { getByPlaceholderText, getByText } = renderLoginScreen();

        fireEvent.changeText(getByPlaceholderText('Enter your email address'), '<EMAIL>');
        fireEvent.press(getByText('Continue with Email'));

        await waitFor(() => expect(getByText(/Resend in \d+s/)).toBeTruthy());

        jest.advanceTimersByTime(60000);

        await waitFor(() => expect(getByText('Resend code')).toBeTruthy());
        
        fireEvent.press(getByText('Resend code'));
        
        await waitFor(() => {
            expect(emailOtpService.sendEmailOTP).toHaveBeenCalledTimes(2);
        });

        jest.useRealTimers();
    });
  });

  describe('Mobile/Password Flow', () => {
    it('should switch to the mobile/password form and allow successful login', async () => {
        (MobileAuthService.signInWithMobilePassword as jest.Mock).mockResolvedValue({ data: { user: { id: '123' } }, error: null });
  
        const { getByText, getByPlaceholderText } = renderLoginScreen();
  
        fireEvent.press(getByText('Phone'));
  
        const mobileInput = getByPlaceholderText('9876543210');
        const passwordInput = getByPlaceholderText('••••••••');
  
        fireEvent.changeText(mobileInput, '1234567890');
        fireEvent.changeText(passwordInput, 'password123');
  
        fireEvent.press(getByText('Sign In'));
  
        await waitFor(() => {
          expect(MobileAuthService.signInWithMobilePassword).toHaveBeenCalledWith('1234567890', 'password123');
          expect(router.replace).toHaveBeenCalledWith('/(dashboard)/customer');
        });
    });

    it('should show validation errors for mobile and password fields', async () => {
        const { getByText, findByText } = renderLoginScreen();

        fireEvent.press(getByText('Phone'));
        fireEvent.press(getByText('Sign In'));

        expect(await findByText('Mobile number is required')).toBeTruthy();
        expect(await findByText('Password is required')).toBeTruthy();
        expect(MobileAuthService.signInWithMobilePassword).not.toHaveBeenCalled();
    });

    it('should handle mobile/password login failure from the server', async () => {
        (MobileAuthService.signInWithMobilePassword as jest.Mock).mockResolvedValue({ data: {}, error: { message: 'Invalid credentials' } });
        const { getByText, getByPlaceholderText, findByText } = renderLoginScreen();

        fireEvent.press(getByText('Phone'));

        fireEvent.changeText(getByPlaceholderText('9876543210'), '1234567890');
        fireEvent.changeText(getByPlaceholderText('••••••••'), 'wrongpassword');
        fireEvent.press(getByText('Sign In'));

        const errorMessage = await findByText('Invalid credentials');
        expect(errorMessage).toBeTruthy();
        expect(router.replace).not.toHaveBeenCalled();
    });
  });

  describe('Google Sign-In Flow', () => {
    it('should handle successful Google Sign-In', async () => {
        (nativeGoogleAuth.signInWithGoogleNative as jest.Mock).mockResolvedValue({ success: true, user: { id: 'google-user' } });
  
        const { getByText } = renderLoginScreen();
  
        fireEvent.press(getByText('Continue with Google'));
  
        await waitFor(() => {
          expect(nativeGoogleAuth.signInWithGoogleNative).toHaveBeenCalled();
          expect(router.replace).toHaveBeenCalledWith('/(dashboard)/customer');
        });
    });

    it('should handle Google Sign-In cancellation by the user', async () => {
        (nativeGoogleAuth.signInWithGoogleNative as jest.Mock).mockResolvedValue({ success: false, message: 'cancelled' });
        const { getByText } = renderLoginScreen();

        fireEvent.press(getByText('Continue with Google'));

        await waitFor(() => {
            expect(nativeGoogleAuth.signInWithGoogleNative).toHaveBeenCalled();
        });
        expect(router.replace).not.toHaveBeenCalled();
    });

    it('should handle Google Sign-In failure from the server', async () => {
        (nativeGoogleAuth.signInWithGoogleNative as jest.Mock).mockResolvedValue({ success: false, message: 'Google sign-in failed' });
        const { getByText } = renderLoginScreen();

        fireEvent.press(getByText('Continue with Google'));

        await waitFor(() => {
            expect(nativeGoogleAuth.signInWithGoogleNative).toHaveBeenCalled();
        });
        expect(router.replace).not.toHaveBeenCalled();
    });
  });

  describe('Navigation Flow', () => {
    it('should navigate to choose-role if the user needs to select a role', async () => {
        (emailOtpService.sendEmailOTP as jest.Mock).mockResolvedValue({ success: true });
        (emailOtpService.verifyEmailOTP as jest.Mock).mockResolvedValue({ success: true });
        
        const { useAuth }: { useAuth: any } = jest.requireMock('@/src/contexts/AuthContext');
        useAuth().checkUserRole.mockResolvedValue({
            needsRoleSelection: true,
            needsOnboarding: false,
            role: null,
        });

        const { getByPlaceholderText, getByText, getByLabelText } = renderLoginScreen();

        fireEvent.changeText(getByPlaceholderText('Enter your email address'), '<EMAIL>');
        fireEvent.press(getByText('Continue with Email'));

        await waitFor(() => expect(emailOtpService.sendEmailOTP).toHaveBeenCalled());

        const otpInput = getByLabelText('Enter verification code');
        fireEvent.changeText(otpInput, '123456');

        await waitFor(() => {
            expect(router.replace).toHaveBeenCalledWith('/(auth)/choose-role');
        });
    });

    it('should navigate to onboarding if the user needs to complete their profile', async () => {
        (emailOtpService.sendEmailOTP as jest.Mock).mockResolvedValue({ success: true });
        (emailOtpService.verifyEmailOTP as jest.Mock).mockResolvedValue({ success: true });
        
        const { useAuth }: { useAuth: any } = jest.requireMock('@/src/contexts/AuthContext');
        useAuth().checkUserRole.mockResolvedValue({
            needsRoleSelection: false,
            needsOnboarding: true,
            role: 'business',
        });

        const { getByPlaceholderText, getByText, getByLabelText } = renderLoginScreen();

        fireEvent.changeText(getByPlaceholderText('Enter your email address'), '<EMAIL>');
        fireEvent.press(getByText('Continue with Email'));

        await waitFor(() => expect(emailOtpService.sendEmailOTP).toHaveBeenCalled());

        const otpInput = getByLabelText('Enter verification code');
        fireEvent.changeText(otpInput, '123456');

        await waitFor(() => {
            expect(router.replace).toHaveBeenCalledWith('/(onboarding)/business-details');
        });
    });
  });
});