import React from 'react';
import { render, fireEvent, act, RenderResult } from '@testing-library/react-native';
import LoginScreen from '@/app/(auth)/login';
import { measurePerformance } from 'reassure';
import * as emailOtpService from '@/backend/supabase/services/auth/emailOtpService';
import { MobileAuthService } from '@/backend/supabase/services/auth/mobileAuthService';

// Mock dependencies
jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({
    checkUserRole: jest.fn().mockResolvedValue({
      needsRoleSelection: false,
      needsOnboarding: false,
      role: 'customer',
    }),
  }),
}));

jest.mock('expo-router', () => ({
  router: {
    replace: jest.fn(),
  },
}));

jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
  }),
}));

describe('Login Screen - Performance Tests', () => {
  // Test 1: Component rendering performance
  test('should render the login screen within performance budget', async () => {
    await measurePerformance(<LoginScreen />);
  });

  // Test 2: Responsiveness of email input and submission
  test('should maintain responsiveness during email login flow', async () => {
    const scenario = async (screen: RenderResult) => {
      const emailInput = await screen.findByLabelText('Email Address');
      const submitButton = await screen.findByText('Continue with Email');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(submitButton);
    };

    await measurePerformance(<LoginScreen />, { scenario });
  });

  // Test 3: Memory leak test
  test('should not have memory leaks after mounting and unmounting', async () => {
    const scenario = async (screen: RenderResult) => {
      screen.unmount();
    };

    // Reassure's measurePerformance can help detect significant memory usage changes
    // by observing the heap size before and after runs.
    await measurePerformance(<LoginScreen />, { scenario });
  });

  // Test 4: Concurrent user login simulation (Email OTP)
  test('should handle concurrent email OTP requests', async () => {
    const sendEmailOTPSpy = jest.spyOn(emailOtpService, 'sendEmailOTP').mockResolvedValue({ success: true, message: 'OTP sent' });

    const scenario = async () => {
      const { getByLabelText, getByText } = render(<LoginScreen />);
      const emailInput = getByLabelText('Email Address');
      const submitButton = getByText('Continue with Email');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(submitButton);
    };

    const promises = Array(10).fill(null).map(() => act(scenario));
    await Promise.all(promises);

    expect(sendEmailOTPSpy).toHaveBeenCalledTimes(10);
  });

  // Test 5: Concurrent user login simulation (Mobile/Password)
  test('should handle concurrent mobile/password login requests', async () => {
    const signInSpy = jest.spyOn(MobileAuthService, 'signInWithMobilePassword').mockResolvedValue({
      data: { user: { id: '123' } as any, session: {} as any },
      error: null,
    });

    const scenario = async () => {
      const { getByLabelText, getByText, getByPlaceholderText } = render(<LoginScreen />);
      fireEvent.press(getByText('Phone'));

      const mobileInput = getByPlaceholderText('9876543210');
      const passwordInput = getByLabelText('Password');
      const submitButton = getByText('Sign In');

      fireEvent.changeText(mobileInput, '1234567890');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(submitButton);
    };

    const promises = Array(10).fill(null).map(() => act(scenario));
    await Promise.all(promises);

    expect(signInSpy).toHaveBeenCalledTimes(10);
  });
});